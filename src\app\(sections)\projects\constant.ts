export const ProjectData = [
  {
    title: "Carbon Calculator",
    description:
      "An application where users can calculate their carbon footprint.",
    techstack: ["React", "Node", "Express", "MongoDB", "typeScript"],
    live: "https://carbonee-calculator.vercel.app/",
    github: "https://github.com/khairibzd/Carbon-Calculator",
  },
  {
    title: "Ai Mock Interview",
    description:
      "A website where users can practice their job interview with questions generated from Ai additionnaly it provides feedback upon interview completion",
    techstack: ["React", "Next Js", "Prompt engineer", "Prisma", "Postgres"],
    live: "https://totaltech-ai-mock-interview.vercel.app/",
    github: "https://github.com/khairibzd",
  },

  {
    title: "DealDiscover",
    description:
      "A recommendation platform with a chatbot that helps users find their specific destination.",
    techstack: [
      "Vue",
      "Pinia",
      "Rasa platform",
      "Python",
      "TypeScript",
      "MongoD",
    ],
    live: "https://deal-discover-vue.vercel.app/",
    github: "https://github.com/medkira/DealDiscover_vue",
  },
   {
    title: "Email Reply Agent",
    description:
      "An intelligent email assistant analyzes incoming messages and generates contextually appropriate responses.",
    techstack: [
      "HTML",
      "CSS",
      "Python",
      "Flask",
      "Prompt Engineering",
      "Sqlite",
    ],
    live: "https://emailagentreplay.me/",
    github: "#",
  },
   {
    title: "E-waste",
    description:
      "E-Waste Management Platform is an application that transforms electronic waste management through AI, blockchain, and face recognition.",
    techstack: [
      "Symfony",
      "Python",
      "JavaScript",
      "Solidity",
      "Iot",
      "Mysql",
    ],
    live: "ewaste-symfony.azurewebsites.net",
    github: "https://github.com/khairiEsprit/e-waste_symfony",
  },
];
